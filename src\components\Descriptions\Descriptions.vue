<template>
  <div :class="['m-descriptions', `descriptions-${size}`]">
    <!-- 头部标题和额外内容 -->
    <div v-if="showHeader" class="descriptions-header">
      <div class="descriptions-title">
        <slot name="title">{{ title }}</slot>
      </div>
      <div class="descriptions-extra">
        <slot name="extra">{{ extra }}</slot>
      </div>
    </div>

    <!-- 垂直布局 -->
    <div
      v-if="vertical"
      :class="['descriptions-view', { 'descriptions-bordered': bordered }]"
    >
      <table>
        <tbody v-if="bordered">
          <template v-for="(row, rowIndex) in rows" :key="rowIndex">
            <tr ref="thVerticalBorderedRows" class="descriptions-bordered-tr"></tr>
            <tr ref="tdVerticalBorderedRows" class="descriptions-bordered-tr"></tr>
          </template>
        </tbody>
        <tbody v-else>
          <template v-for="(row, rowIndex) in rows" :key="rowIndex">
            <tr>
              <th
                v-for="(item, itemIndex) in row"
                :key="itemIndex"
                class="descriptions-item-th"
                :colspan="item.span"
              >
                <div ref="thVerticalCols" class="descriptions-item"></div>
              </th>
            </tr>
            <tr>
              <td
                v-for="(item, itemIndex) in row"
                :key="itemIndex"
                class="descriptions-item-td"
                :colspan="item.span"
              >
                <div ref="tdVerticalCols" class="descriptions-item"></div>
              </td>
            </tr>
          </template>
        </tbody>
      </table>
    </div>

    <!-- 水平布局 -->
    <div
      v-else
      :class="['descriptions-view', { 'descriptions-bordered': bordered }]"
    >
      <table>
        <tbody v-if="bordered">
          <tr
            v-for="(row, rowIndex) in rows"
            :key="rowIndex"
            ref="trBorderedRows"
            class="descriptions-bordered-tr"
          ></tr>
        </tbody>
        <tbody v-else>
          <tr v-for="(row, rowIndex) in rows" :key="rowIndex">
            <td
              v-for="(item, itemIndex) in row"
              :key="itemIndex"
              ref="tdCols"
              class="descriptions-item-td"
              :colspan="item.span"
            ></td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 隐藏的默认插槽内容 -->
    <div ref="defaultSlotsRef" v-show="false">
      <slot :key="forceUpdate"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { useSlots } from 'vue'

interface Props {
  title?: string
  extra?: string
  bordered?: boolean
  vertical?: boolean
  size?: 'default' | 'middle' | 'small'
  column?: number | Record<string, number>
  labelStyle?: Record<string, any>
  contentStyle?: Record<string, any>
}

const props = withDefaults(defineProps<Props>(), {
  title: undefined,
  extra: undefined,
  bordered: false,
  vertical: false,
  size: 'default',
  column: () => ({ xs: 1, sm: 2, md: 3 }),
  labelStyle: () => ({}),
  contentStyle: () => ({})
})

const slots = useSlots()

// 响应式数据
const defaultSlotsRef = ref<HTMLElement>()
const forceUpdate = ref(true)
const needUpdate = ref(true)
const itemElements = ref<HTMLElement[]>([])
const tdCols = ref<HTMLElement[]>([])
const thVerticalCols = ref<HTMLElement[]>([])
const tdVerticalCols = ref<HTMLElement[]>([])
const trBorderedRows = ref<HTMLElement[]>([])
const thVerticalBorderedRows = ref<HTMLElement[]>([])
const tdVerticalBorderedRows = ref<HTMLElement[]>([])
const rows = ref<Array<Array<{ span: number; element: HTMLElement }>>>([])
const windowWidth = ref(window.innerWidth)

// 计算属性
const showHeader = computed(() => {
  return slots.title || slots.extra || props.title || props.extra
})

const currentColumn = computed(() => {
  if (typeof props.column === 'object') {
    if (windowWidth.value >= 1600 && props.column.xxl !== undefined) return props.column.xxl
    if (windowWidth.value >= 1200 && props.column.xl !== undefined) return props.column.xl
    if (windowWidth.value >= 992 && props.column.lg !== undefined) return props.column.lg
    if (windowWidth.value >= 768 && props.column.md !== undefined) return props.column.md
    if (windowWidth.value >= 576 && props.column.sm !== undefined) return props.column.sm
    if (windowWidth.value < 576 && props.column.xs !== undefined) return props.column.xs
    return 1
  }
  return props.column
})

// 监听器
watch(
  () => [props.bordered, props.vertical, currentColumn.value, props.labelStyle, props.contentStyle],
  () => {
    if (!needUpdate.value) needUpdate.value = true
    updateLayout()
  },
  { deep: true }
)

// 窗口大小变化监听
const handleResize = () => {
  windowWidth.value = window.innerWidth
}

// 生命周期
onMounted(() => {
  window.addEventListener('resize', handleResize)
  parseItems()
})

// 方法
const getRowSpan = (row: Array<{ span: number; element: HTMLElement }>) => {
  return row.reduce((total, item) => total + item.span, 0)
}

const updateLayout = async () => {
  forceUpdate.value = !forceUpdate.value
  await nextTick()
  parseItems()
}

const parseItems = async () => {
  if (!defaultSlotsRef.value) return

  itemElements.value = Array.from(defaultSlotsRef.value.children).filter(
    (el: any) => el.className === (props.bordered ? 'descriptions-item-bordered' : 'descriptions-item')
  ) as HTMLElement[]

  if (rows.value.length) {
    rows.value = []
    await nextTick()
  }

  if (itemElements.value && itemElements.value.length) {
    const totalItems = itemElements.value.length
    let currentRow: Array<{ span: number; element: HTMLElement }> = []

    for (let i = 0; i < totalItems; i++) {
      const item = {
        span: Math.min(Number(itemElements.value[i].dataset.span) || 1, currentColumn.value),
        element: itemElements.value[i]
      }

      if (getRowSpan(currentRow) < currentColumn.value) {
        item.span = Math.min(item.span, currentColumn.value - getRowSpan(currentRow))
        currentRow.push(item)
      } else {
        rows.value.push(currentRow)
        currentRow = [item]
      }
    }

    // 处理最后一行的跨度
    if (!props.vertical && !itemElements.value[totalItems - 1].dataset.span && getRowSpan(currentRow) < currentColumn.value) {
      const lastIndex = currentRow.length - 1
      currentRow[lastIndex].span = currentRow[lastIndex].span + currentColumn.value - getRowSpan(currentRow)
    }

    rows.value.push(currentRow)
    await nextTick()
    renderItems()
  } else {
    needUpdate.value = false
  }
}

const renderItems = async () => {
  if (props.bordered) {
    rows.value.forEach((row, rowIndex) => {
      row.forEach((item) => {
        const children = Array.from(item.element.children) as HTMLElement[]
        const labelEl = children[0]
        const contentEl = children[1]

        applyStyles(labelEl, props.labelStyle)
        applyStyles(contentEl, props.contentStyle)

        if (props.vertical) {
          labelEl.colSpan = item.span
          contentEl.colSpan = item.span
          thVerticalBorderedRows.value[rowIndex].appendChild(labelEl)
          tdVerticalBorderedRows.value[rowIndex].appendChild(contentEl)
        } else {
          labelEl.colSpan = 1
          contentEl.colSpan = item.span * 2 - 1
          trBorderedRows.value[rowIndex].appendChild(labelEl)
          trBorderedRows.value[rowIndex].appendChild(contentEl)
        }
      })
    })
  } else {
    itemElements.value.forEach((element, index) => {
      const children = Array.from(element.children) as HTMLElement[]
      const labelEl = children[0]
      const contentEl = children[1]

      applyStyles(labelEl, props.labelStyle)
      applyStyles(contentEl, props.contentStyle)

      if (props.vertical) {
        thVerticalCols.value[index].appendChild(element.firstChild!)
        tdVerticalCols.value[index].appendChild(element.lastChild!)
      } else {
        tdCols.value[index].appendChild(element)
      }
    })
  }

  await nextTick()
  needUpdate.value = false
}

const applyStyles = (element: HTMLElement, styles: Record<string, any>) => {
  if (JSON.stringify(styles) !== '{}') {
    Object.keys(styles).forEach((key) => {
      if (!element.style[key as any]) {
        element.style[key as any] = styles[key]
      }
    })
  }
}
</script>

<style scoped>
/* 组件样式将通过外部 CSS 文件引入 */
</style>
