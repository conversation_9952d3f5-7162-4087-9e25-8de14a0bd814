<template>
  <!-- 普通模式的描述项 -->
  <div
    class="descriptions-item"
    :data-span="span"
  >
    <span
      class="descriptions-label"
      :style="labelStyle"
    >
      <slot name="label">{{ label }}</slot>
    </span>
    <span
      class="descriptions-content"
      :style="contentStyle"
    >
      <slot></slot>
    </span>
  </div>

  <!-- 边框模式的描述项 -->
  <tr
    class="descriptions-item-bordered"
    :data-span="span"
  >
    <th
      class="descriptions-label-th"
      :style="labelStyle"
    >
      <slot name="label">{{ label }}</slot>
    </th>
    <td
      class="descriptions-content-td"
      :style="contentStyle"
    >
      <slot></slot>
    </td>
  </tr>
</template>

<script setup lang="ts">
interface Props {
  label?: string
  span?: number
  labelStyle?: Record<string, any>
  contentStyle?: Record<string, any>
}

withDefaults(defineProps<Props>(), {
  label: undefined,
  span: undefined,
  labelStyle: () => ({}),
  contentStyle: () => ({})
})
</script>

<style scoped>
/* 组件样式将通过外部 CSS 文件引入 */
</style>
