/* Descriptions 组件样式 */
.m-descriptions {
  font-size: 14px;
  color: #000000e0;
  line-height: 1.57142857;
}

.m-descriptions .descriptions-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.m-descriptions .descriptions-header .descriptions-title {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  flex: auto;
  font-weight: 600;
  font-size: 16px;
  color: #000000e0;
  line-height: 1.57142857;
}

.m-descriptions .descriptions-header .descriptions-extra {
  margin-left: auto;
  color: #000000e0;
  font-size: 14px;
}

.m-descriptions .descriptions-view {
  width: 100%;
  border-radius: 8px;
}

.m-descriptions .descriptions-view table {
  width: 100%;
  table-layout: fixed;
  display: table;
  border-collapse: separate;
  margin: 0;
}

.m-descriptions .descriptions-view table tr {
  border: none;
  background: transparent;
}

.m-descriptions .descriptions-view table .descriptions-item-th {
  padding: 0 0 16px;
  border: none;
  vertical-align: top;
  background: transparent;
}

.m-descriptions .descriptions-view table .descriptions-item-td {
  padding: 0 0 16px;
  border: none;
  vertical-align: top;
}

.m-descriptions .descriptions-view table .descriptions-item {
  display: flex;
}

.m-descriptions .descriptions-bordered {
  border: 1px solid rgba(5, 5, 5, 0.06);
}

.m-descriptions .descriptions-bordered table {
  table-layout: auto;
  border-collapse: collapse;
  display: table;
  margin: 0;
}

.m-descriptions .descriptions-bordered table .descriptions-bordered-tr {
  border-bottom: 1px solid rgba(5, 5, 5, 0.06);
}

.m-descriptions .descriptions-bordered table .descriptions-bordered-tr:last-child {
  border-bottom: none;
}

.m-descriptions .descriptions-bordered table .descriptions-bordered-tr .descriptions-label-th {
  border: none;
  color: #000000e0;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.57142857;
  text-align: start;
  background-color: #00000005;
  padding: 16px 24px;
  border-right: 1px solid rgba(5, 5, 5, 0.06);
}

.m-descriptions .descriptions-bordered table .descriptions-bordered-tr .descriptions-label-th:last-child {
  border-right: none;
}

.m-descriptions .descriptions-bordered table .descriptions-bordered-tr .descriptions-content-td {
  border: none;
  display: table-cell;
  flex: 1;
  padding: 16px 24px;
  border-right: 1px solid rgba(5, 5, 5, 0.06);
  color: #000000e0;
  font-size: 14px;
  line-height: 1.57142857;
  word-break: break-word;
  overflow-wrap: break-word;
}

.m-descriptions .descriptions-bordered table .descriptions-bordered-tr .descriptions-content-td:last-child {
  border-right: none;
}

/* 中等尺寸样式 */
.descriptions-middle .descriptions-view .descriptions-item-td {
  padding-bottom: 12px !important;
}

.descriptions-middle .descriptions-bordered .descriptions-label-th,
.descriptions-middle .descriptions-bordered .descriptions-content-td {
  padding: 12px 24px !important;
}

/* 小尺寸样式 */
.descriptions-small .descriptions-view .descriptions-item-td {
  padding-bottom: 8px !important;
}

.descriptions-small .descriptions-bordered .descriptions-label-th,
.descriptions-small .descriptions-bordered .descriptions-content-td {
  padding: 8px 16px !important;
}

/* DescriptionsItem 组件样式 */
.descriptions-item {
  display: flex;
}

.descriptions-item .descriptions-label {
  display: inline-flex;
  align-items: baseline;
  color: #000000e0;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.57142857;
  text-align: start;
}

.descriptions-item .descriptions-label:after {
  content: ":";
  position: relative;
  top: -0.5px;
  margin-inline: 2px 8px;
}

.descriptions-item .descriptions-content {
  display: inline-flex;
  align-items: baseline;
  flex: 1;
  color: #000000e0;
  font-size: 14px;
  line-height: 1.57142857;
  word-break: break-word;
  overflow-wrap: break-word;
}
