<!-- 场景预案 -->
<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="mb8 w-full" >
					<div class="float-left">
						<el-form ref="queryRef" :inline="true" :model="state.queryForm" @keyup.enter="getDataList">
							<el-form-item label="入库单编号" prop="phone">
								<el-input v-model="state.queryForm.phone" placeholder="请输入" clearable style="max-width: 180px" />
							</el-form-item>
							<el-form-item label="入库状态" prop="logType">
								<el-select placeholder="请选择" clearable v-model="state.queryForm.logType">
									<el-option :key="item.value" :label="item.label" :value="item.value" v-for="item in warnType" />
								</el-select>
							</el-form-item>
							<el-form-item label="入库仓库" prop="logType">
								<el-select placeholder="请选择" clearable v-model="state.queryForm.logType">
									<el-option :key="item.value" :label="item.label" :value="item.value" v-for="item in warnType" />
								</el-select>
							</el-form-item>
							<el-form-item label="入库时间" prop="createTime">
								<el-date-picker
									:end-placeholder="$t('syslog.inputEndPlaceholderTip')"
									:start-placeholder="$t('syslog.inputStartPlaceholderTip')"
									range-separator="To"
									type="datetimerange"
									v-model="state.queryForm.createTime"
									value-format="YYYY-MM-DD HH:mm:ss"
								/>
							</el-form-item>
							<el-form-item>
								<el-button icon="Search" type="primary" @click="getDataList">{{ $t('common.queryBtn') }}</el-button>
							</el-form-item>
						</el-form>
					</div>
					<div class="float-right">
						<el-button v-auth="'sys_user_add'" icon="folder-add" type="primary" @click="routerClick">
							{{ $t('common.addBtn') }}
						</el-button>
					</div>
				</div>
			</el-row>

			<el-table
				v-loading="state.loading"
				:data="state.dataList"
				@selection-change="handleSelectionChange"
				row-key="userId"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column label="序号" type="index" width="60" fixed="left" />
				<el-table-column label="入库单编号" prop="name" show-overflow-tooltip></el-table-column>
				<el-table-column label="入库单状态" prop="name" show-overflow-tooltip></el-table-column>
				<el-table-column label="入库仓库" prop="name" show-overflow-tooltip></el-table-column>
				<el-table-column label="入库部门" prop="name" show-overflow-tooltip></el-table-column>
				<el-table-column label="入库人员" prop="name" show-overflow-tooltip></el-table-column>
				<el-table-column label="入库时间" prop="name" show-overflow-tooltip></el-table-column>
				<el-table-column label="创建人" prop="name" show-overflow-tooltip></el-table-column>
				<el-table-column label="创建时间" prop="name" show-overflow-tooltip></el-table-column>
				<el-table-column :label="$t('common.action')" width="200" fixed="right">
					<template #default="scope">
						<!-- 上架指引-->
						<el-button v-auth="'sys_user_edit'" text type="primary" @click="guideClick(scope.row.userId)"> 上架指引 </el-button>
						<!-- 查看 入库单 -->
						<el-button v-auth="'sys_user_edit'" text type="primary" @click="formClick(scope.row.userId)"> 查看 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination v-bind="state.pagination" @current-change="currentChangeHandle" @size-change="sizeChangeHandle"> </pagination>
		</div>
	</div>
</template>

<script lang="ts" name="systemUser" setup>
import { delObj, pageList, putObj } from '/@/api/admin/user';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
import { useDict } from '/@/hooks/dict';

// 定义查询字典
const { warnType } = useDict('warnType');
// 动态引入组件

const { t } = useI18n();

// 定义变量内容
const userDialogRef = ref();
const queryRef = ref();

const param_type = ref([]);
// 多选rows
const selectObjs = ref([]) as any;
// 是否可以多选
const multiple = ref(true);

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		deptId: '',
		username: '',
		phone: '',
	},
	pageList: pageList,
});
const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile, tableStyle } = useTable(state);

// 多选事件
const handleSelectionChange = (objs: { userId: string }[]) => {
	selectObjs.value = objs.map(({ userId }) => userId);
	multiple.value = !objs.length;
};

//表格内开关 (用户状态)
const changeSwitch = async (row: any) => {
	// 不修改密码
	row.password = undefined;
	row.phone = undefined;
	await putObj(row);
	useMessage().success(t('common.optSuccessText'));
	getDataList();
};
// 删除操作
const handleDelete = async (ids: string[]) => {
	try {
		await useMessageBox().confirm(t('common.delConfirmText'));
	} catch {
		return;
	}

	try {
		await delObj(ids);
		getDataList();
		useMessage().success(t('common.delSuccessText'));
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};

//查看入库单页面
const formClick = (id?: any) => {
	router.push({
		path: '/storage/warehouseManagement/other/form',
		query: { id: id, notCreateTags: 'true' },
	});
};
//上架指引
const guideClick = (id?: any) => {
	router.push({
		path: '/storage/warehouseManagement/other/guide',
		query: { id: id, notCreateTags: 'true' },
	});
};

//新增 修改页面
const router = useRouter();
const routerClick = (id?: any) => {
	const tagsViewName = id ? `修改:${id}` : '新增';
	router.push({
		path: '/storage/warehouseManagement/other/add',
		query: { id: id, tagsViewName: tagsViewName, notCreateTags: 'true' },
	});
};
</script>
